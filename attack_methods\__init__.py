"""
大模型安全攻击方法模块
包含设定类、加密类、劫持类等攻击方法的实现
"""

from .setting_based import *
from .encryption_based import *
from .hijacking_based import *

__all__ = [
    # 设定类方法
    'negative_role_attack',
    'positive_role_attack',
    'opposite_response_attack',
    'literary_creation_attack',
    'background_task_attack',
    
    # 加密类方法
    'encryption_dialogue_attack',
    'text_disorder_attack',
    'separator_attack',
    'chinese_specific_attack',
    'language_type_attack',
    'arrangement_operation_attack',
    'code_operation_attack',
    'math_operation_attack',
    'long_context_attack',
    
    # 劫持类方法
    'direct_repeat_attack',
    'specified_beginning_attack',
    'false_knowledge_attack',
    'fake_timeline_attack',
]
