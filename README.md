# 大模型安全攻击方法开发框架

## 项目概述
本项目用于开发通用的大模型安全攻击方法，将普通有害提示转化为能绕过安全策略的攻击提示。

## 攻击方法分类
- **设定类**: 人物设定、情景设定
- **加密类**: 字符加密、运算加密、无关加密  
- **劫持类**: 观点劫持、知识劫持

## 项目结构
```
├── attack_methods/          # 攻击方法实现
│   ├── setting_based/       # 设定类方法
│   ├── encryption_based/    # 加密类方法
│   └── hijacking_based/     # 劫持类方法
├── utils/                   # 工具函数
├── tests/                   # 测试用例
├── examples/                # 示例代码
└── submission/              # 提交代码生成
```

## 使用方法
1. 选择或开发攻击方法
2. 使用测试工具验证效果
3. 生成符合比赛要求的提交代码

## 注意事项
- 所有方法需要具备通用性和一致性
- 提交代码必须符合比赛规定格式
- 禁止使用网络服务或高成本模型
